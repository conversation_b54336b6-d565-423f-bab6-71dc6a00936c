{"hash": "65961a17", "configHash": "38afd278", "lockfileHash": "7e4caa6d", "browserHash": "fc704a66", "optimized": {"react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "ea83820a", "needsInterop": true}, "react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "cff81622", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "a3f60a00", "needsInterop": true}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "555d3fbb", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "076f6071", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "75a4f11c", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "abf8c2e6", "needsInterop": false}}, "chunks": {"chunk-UA5VLOFU": {"file": "chunk-UA5VLOFU.js"}, "chunk-52H2PBNT": {"file": "chunk-52H2PBNT.js"}, "chunk-UYNX3HOC": {"file": "chunk-UYNX3HOC.js"}}}