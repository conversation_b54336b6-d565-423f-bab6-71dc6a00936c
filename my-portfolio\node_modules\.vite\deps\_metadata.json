{"hash": "fe68a617", "configHash": "d95eaae6", "lockfileHash": "7e4caa6d", "browserHash": "38fccd7e", "optimized": {"react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "90c5d19a", "needsInterop": true}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "75e3b956", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "79d55e34", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "dad3e410", "needsInterop": false}, "react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "20c95474", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "0909c3db", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "c33b96e4", "needsInterop": false}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "acc0679e", "needsInterop": true}}, "chunks": {"chunk-HL5FOTE3": {"file": "chunk-HL5FOTE3.js"}, "chunk-UA5VLOFU": {"file": "chunk-UA5VLOFU.js"}, "chunk-UYNX3HOC": {"file": "chunk-UYNX3HOC.js"}}}