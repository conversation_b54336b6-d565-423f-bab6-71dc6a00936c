import React from 'react';
import { motion } from 'framer-motion';
import { Award, BookOpen } from 'lucide-react';

const EducationSection = () => {
  const education = [
    {
      degree: "Bachelor of Science in Computer Science",
      school: "Tech University",
      period: "2016 - 2020",
      gpa: "3.8/4.0",
      description: "Specialized in software engineering and algorithms. Graduated summa cum laude."
    }
  ];

  const certifications = [
    {
      title: "AWS Certified Solutions Architect",
      issuer: "Amazon Web Services",
      year: "2023"
    },
    {
      title: "React Developer Certification",
      issuer: "Meta",
      year: "2022"
    },
    {
      title: "Google Cloud Professional",
      issuer: "Google Cloud",
      year: "2022"
    }
  ];

  return (
    <section id="education" className="py-20 px-6 relative">
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ y: 50, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold mb-4 bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
            Education & Certifications
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-cyan-400 to-purple-400 mx-auto rounded-full" />
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* Education */}
          <motion.div
            initial={{ x: -100, opacity: 0 }}
            whileInView={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center gap-3 mb-8">
              <div className="p-3 bg-gradient-to-r from-cyan-400/20 to-purple-400/20 rounded-xl">
                <BookOpen size={24} className="text-cyan-400" />
              </div>
              <h3 className="text-2xl font-bold text-white">Education</h3>
            </div>
            
            {education.map((edu, index) => (
              <div key={index} className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl p-8">
                <h4 className="text-xl font-bold text-white mb-2">{edu.degree}</h4>
                <p className="text-cyan-400 font-medium mb-2">{edu.school}</p>
                <div className="flex justify-between items-center mb-4">
                  <span className="text-purple-400">{edu.period}</span>
                  <span className="text-pink-400 font-medium">GPA: {edu.gpa}</span>
                </div>
                <p className="text-gray-300">{edu.description}</p>
              </div>
            ))}
          </motion.div>

          {/* Certifications */}
          <motion.div
            initial={{ x: 100, opacity: 0 }}
            whileInView={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center gap-3 mb-8">
              <div className="p-3 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-xl">
                <Award size={24} className="text-purple-400" />
              </div>
              <h3 className="text-2xl font-bold text-white">Certifications</h3>
            </div>
            
            <div className="space-y-4">
              {certifications.map((cert, index) => (
                <motion.div
                  key={index}
                  className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl p-6 hover:bg-white/15 transition-all duration-300"
                  initial={{ y: 20, opacity: 0 }}
                  whileInView={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <h4 className="text-lg font-bold text-white mb-2">{cert.title}</h4>
                  <div className="flex justify-between items-center">
                    <p className="text-cyan-400">{cert.issuer}</p>
                    <span className="text-purple-400 font-medium">{cert.year}</span>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default EducationSection;
