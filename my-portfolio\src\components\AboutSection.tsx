import React from 'react';
import { motion } from 'framer-motion';
import { Heart, Coffee, Code2, Gamepad2 } from 'lucide-react';

const AboutSection = () => {
  const funFacts = [
    { icon: Coffee, text: "Coffee enthusiast - 4 cups/day minimum" },
    { icon: Code2, text: "5+ years building digital experiences" },
    { icon: Heart, text: "Passionate about clean, efficient code" },
    { icon: Gamepad2, text: "Retro gaming collector in spare time" }
  ];

  return (
    <section id="about" className="py-20 px-6 relative">
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ y: 50, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold mb-4 bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
            About Me
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-cyan-400 to-purple-400 mx-auto rounded-full" />
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <motion.div
            initial={{ x: -100, opacity: 0 }}
            whileInView={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            <div className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl p-8">
              <h3 className="text-2xl font-bold text-cyan-400 mb-4">My Story</h3>
              <p className="text-gray-300 leading-relaxed mb-4">
                Started my journey in tech 5 years ago with a simple "Hello, World!" and never looked back. 
                What began as curiosity quickly evolved into a passion for creating digital solutions that make a difference.
              </p>
              <p className="text-gray-300 leading-relaxed mb-4">
                I specialize in full-stack development with a focus on modern JavaScript frameworks, cloud architecture, 
                and user-centered design. Every project is an opportunity to learn something new and push the boundaries 
                of what's possible.
              </p>
              <p className="text-gray-300 leading-relaxed">
                When I'm not coding, you'll find me exploring the latest tech trends, contributing to open source, 
                or planning my next adventure.
              </p>
            </div>
          </motion.div>

          <motion.div
            initial={{ x: 100, opacity: 0 }}
            whileInView={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            <div className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl p-8">
              <h3 className="text-2xl font-bold text-purple-400 mb-6">Fun Facts</h3>
              <div className="space-y-4">
                {funFacts.map((fact, index) => (
                  <motion.div
                    key={index}
                    className="flex items-center gap-4 p-4 bg-white/5 rounded-xl border border-white/10"
                    initial={{ y: 20, opacity: 0 }}
                    whileInView={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    <div className="p-2 bg-gradient-to-r from-cyan-400/20 to-purple-400/20 rounded-lg">
                      <fact.icon size={24} className="text-cyan-400" />
                    </div>
                    <span className="text-gray-300">{fact.text}</span>
                  </motion.div>
                ))}
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl p-8">
              <h3 className="text-2xl font-bold text-pink-400 mb-4">Philosophy</h3>
              <p className="text-gray-300 leading-relaxed">
                "Great code is not just about solving problems—it's about crafting elegant solutions 
                that are maintainable, scalable, and delightful to use. Every line should serve a purpose."
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
