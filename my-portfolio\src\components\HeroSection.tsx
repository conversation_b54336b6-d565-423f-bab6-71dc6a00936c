import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Download } from 'lucide-react';
import { Link } from 'react-router-dom';

const HeroSection = () => {
  const [windowWidth, setWindowWidth] = useState(1024);

  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    handleResize(); // Set initial value
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const techIcons = [
    { name: 'React', symbol: '⚛️', color: 'from-cyan-400 to-blue-500', position: 'top-4 left-4' },
    { name: 'TypeScript', symbol: 'TS', color: 'from-blue-500 to-indigo-500', position: 'top-4 right-4' },
    { name: 'Node.js', symbol: '🟢', color: 'from-green-400 to-emerald-500', position: 'bottom-4 left-4' },
    { name: 'Python', symbol: '🐍', color: 'from-purple-400 to-pink-500', position: 'bottom-4 right-4' },
    { name: 'JavaScript', symbol: 'JS', color: 'from-yellow-400 to-orange-500', position: 'top-1/2 -left-8 -translate-y-1/2' },
    { name: 'MongoDB', symbol: '🍃', color: 'from-green-500 to-teal-500', position: 'top-1/2 -right-8 -translate-y-1/2' },
    { name: 'Docker', symbol: '🐳', color: 'from-blue-400 to-cyan-500', position: '-top-8 left-1/2 -translate-x-1/2' },
    { name: 'AWS', symbol: '☁️', color: 'from-orange-400 to-red-500', position: '-bottom-8 left-1/2 -translate-x-1/2' },
  ];
  
  return (
    <section className="min-h-screen flex items-center justify-center relative px-6 pt-20">
      <div className="max-w-7xl mx-auto grid lg:grid-cols-2 gap-12 items-center">
        {/* Left Content */}
        <motion.div
          initial={{ x: -100, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="space-y-8"
        >
          <motion.div
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <h2 className="text-2xl text-cyan-400 mb-4">Hi, I'm</h2>
            <h1 className="text-5xl lg:text-7xl font-bold leading-tight mb-6">
              <span className="bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
                Jane Doe
              </span>
            </h1>
            <h3 className="text-3xl lg:text-4xl text-gray-300 mb-8">Full Stack Developer</h3>
          </motion.div>

          <motion.p 
            className="text-xl text-gray-400 max-w-2xl leading-relaxed"
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            Passionate about crafting exceptional digital experiences with modern technologies. 
            I transform ideas into scalable, performant applications that users love.
          </motion.p>

          <motion.div 
            className="flex flex-wrap gap-4"
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.8 }}
          >
            <Link to="/projects">
              <motion.button
                className="px-8 py-4 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center gap-2"
                whileHover={{ scale: 1.05, boxShadow: "0 20px 40px rgba(6, 182, 212, 0.3)" }}
                whileTap={{ scale: 0.95 }}
              >
                View Projects
              </motion.button>
            </Link>
            <motion.button
              className="px-8 py-4 bg-gray-800/60 backdrop-blur-sm border border-gray-600/50 rounded-full font-semibold text-lg hover:bg-gray-700/60 transition-all duration-300 flex items-center gap-2"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Download size={20} />
              Resume
            </motion.button>
          </motion.div>
        </motion.div>

        {/* Right Content - Simplified Image Animation */}
        <motion.div
          initial={{ x: 100, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="relative flex justify-center items-center"
        >
          {/* Main Container */}
          <div className="relative w-80 h-80 lg:w-96 lg:h-96">
            
            {/* Subtle Rotating Ring */}
            <motion.div
              className="absolute inset-0 rounded-full border border-cyan-400/20"
              animate={{ rotate: 360 }}
              transition={{ duration: 30, repeat: Infinity, ease: "linear" }}
            />

            {/* Central Image Container with Glassmorphism */}
            <motion.div
              className="absolute inset-8 rounded-full overflow-hidden backdrop-blur-xl bg-gray-900/20 border border-gray-700/30 shadow-2xl"
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 300, damping: 20 }}
            >
              <motion.img
                src="https://images.unsplash.com/photo-1494790108755-2616b169121d?w=400&h=400&fit=crop&crop=face"
                alt="Jane Doe - Full Stack Developer"
                className="w-full h-full object-cover"
              />
              
              {/* Glassmorphism Overlay */}
              <div className="absolute inset-0 bg-gradient-to-tr from-cyan-500/5 via-transparent to-purple-500/5" />
            </motion.div>

            {/* Floating Tech Icons Around the Circle */}
            {techIcons.map((tech, index) => {
              const angle = (index / techIcons.length) * 2 * Math.PI;
              const radius = windowWidth < 768 ? 140 : windowWidth < 1024 ? 160 : 180; // Adjusted for original container size
              const x = Math.cos(angle) * radius;
              const y = Math.sin(angle) * radius;

              return (
                <motion.div
                  key={index}
                  className="absolute z-30"
                  style={{
                    left: `calc(50% + ${x}px)`,
                    top: `calc(50% + ${y}px)`,
                    transform: 'translate(-50%, -50%)'
                  }}
                  initial={{ scale: 0, opacity: 0 }}
                  animate={{
                    scale: 1,
                    opacity: 1,
                  }}
                  transition={{
                    scale: { duration: 0.8, delay: 1.2 + index * 0.15 },
                    opacity: { duration: 0.8, delay: 1.2 + index * 0.15 },
                  }}
                >
                  <motion.div
                    className="relative group cursor-pointer"
                    whileHover={{ scale: 1.2 }}
                    animate={{
                      y: [0, -8, 0], // Gentle floating
                      rotate: [0, 2, -2, 0], // Subtle rotation
                    }}
                    transition={{
                      y: { duration: 3 + (index * 0.2), repeat: Infinity, ease: "easeInOut", delay: index * 0.3 },
                      rotate: { duration: 4 + (index * 0.2), repeat: Infinity, ease: "easeInOut", delay: index * 0.4 },
                      scale: { type: "spring", stiffness: 400, damping: 15 }
                    }}
                  >
                    {/* Glassmorphism Tech Icon Card */}
                    <div className="relative backdrop-blur-xl bg-gray-900/40 border border-gray-700/50 rounded-xl p-2 shadow-2xl min-w-[60px]">
                      {/* Gradient Background */}
                      <div className={`absolute inset-0 bg-gradient-to-br ${tech.color} opacity-10 rounded-xl`} />

                      {/* Icon Content */}
                      <div className="relative flex flex-col items-center gap-1">
                        <span className="text-base">{tech.symbol}</span>
                        <span className="text-[10px] font-medium text-gray-300 whitespace-nowrap">
                          {tech.name}
                        </span>
                      </div>
                      
                      {/* Hover Glow Effect */}
                      <motion.div
                        className={`absolute inset-0 bg-gradient-to-br ${tech.color} opacity-0 rounded-xl blur-xl`}
                        whileHover={{ opacity: 0.3 }}
                        transition={{ duration: 0.3 }}
                      />
                    </div>
                  </motion.div>
                </motion.div>
              );
            })}

            {/* Pulsing Central Glow */}
            <motion.div
              className="absolute inset-12 rounded-full bg-gradient-radial from-cyan-400/10 via-purple-400/5 to-transparent blur-2xl"
              animate={{ 
                scale: [1, 1.1, 1],
                opacity: [0.3, 0.5, 0.3]
              }}
              transition={{ 
                duration: 4, 
                repeat: Infinity, 
                ease: "easeInOut" 
              }}
            />
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default HeroSection;
