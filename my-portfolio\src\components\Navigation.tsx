import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Github, Linkedin, Mail, Menu, X } from 'lucide-react';
import { Link, useLocation } from 'react-router-dom';

const Navigation = () => {
  const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const navItems = [
    { name: 'Home', path: '/' },
    { name: 'Projects', path: '/projects' },
    { name: 'About', path: '/#about' },
    { name: 'Contact', path: '/#contact' },
  ];

  return (
    <motion.nav 
      className="fixed top-0 left-0 right-0 z-50 p-6"
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.8 }}
    >
      <div className="max-w-7xl mx-auto">
        <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl px-6 py-4 shadow-2xl">
          <div className="flex justify-between items-center">
            <Link to="/">
              <motion.div
                className="flex items-center gap-3"
                whileHover={{ scale: 1.05 }}
              >
                <div className="w-10 h-10 rounded-full overflow-hidden border-2 border-cyan-400/30 shadow-lg">
                  <img
                    src="https://images.unsplash.com/photo-1494790108755-2616b169121d?w=100&h=100&fit=crop&crop=face"
                    alt="Jane Doe"
                    className="w-full h-full object-cover"
                  />
                </div>
                <span className="text-2xl font-bold bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
                  Jane Doe
                </span>
              </motion.div>
            </Link>
            
            <div className="flex items-center space-x-8">
              {/* Navigation Links */}
              <div className="hidden md:flex space-x-6">
                {navItems.map((item) => {
                  const isActive = location.pathname === item.path ||
                    (item.path.includes('#') && location.pathname === '/' && location.hash === item.path.split('#')[1]);

                  const handleClick = (e: React.MouseEvent) => {
                    if (item.path.includes('#')) {
                      e.preventDefault();
                      const sectionId = item.path.split('#')[1];
                      const element = document.getElementById(sectionId);
                      if (element) {
                        element.scrollIntoView({ behavior: 'smooth', block: 'start' });
                      }
                    }
                    setIsMobileMenuOpen(false);
                  };

                  return (
                    <Link
                      key={item.path}
                      to={item.path}
                      onClick={handleClick}
                      className={`relative px-3 py-2 text-sm font-medium transition-colors duration-300 ${
                        isActive
                          ? 'text-cyan-400'
                          : 'text-gray-300 hover:text-white'
                      }`}
                    >
                      {item.name}
                      {isActive && (
                        <motion.div
                          className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-cyan-400 to-purple-400 rounded-full"
                          layoutId="activeTab"
                          initial={false}
                          transition={{ type: "spring", stiffness: 500, damping: 30 }}
                        />
                      )}
                    </Link>
                  );
                })}
              </div>
              
              {/* Social Links */}
              <div className="flex space-x-4">
                {[Github, Linkedin, Mail].map((Icon, index) => (
                  <motion.a
                    key={index}
                    href="#"
                    className="p-2 bg-gray-800/60 backdrop-blur-sm border border-gray-600/50 rounded-lg hover:bg-gray-700/60 transition-all duration-300"
                    whileHover={{ scale: 1.1, y: -2 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Icon size={18} className="text-gray-300 hover:text-white transition-colors" />
                  </motion.a>
                ))}
              </div>

              {/* Mobile Menu Button */}
              <motion.button
                className="md:hidden p-2 bg-gray-800/60 backdrop-blur-sm border border-gray-600/50 rounded-lg hover:bg-gray-700/60 transition-all duration-300"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
              >
                {isMobileMenuOpen ? (
                  <X size={20} className="text-gray-300" />
                ) : (
                  <Menu size={20} className="text-gray-300" />
                )}
              </motion.button>
            </div>
          </div>

          {/* Mobile Menu */}
          {isMobileMenuOpen && (
            <motion.div
              className="md:hidden mt-4 bg-gray-900/80 backdrop-blur-xl border border-gray-700/50 rounded-xl p-4"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex flex-col space-y-3">
                {navItems.map((item) => {
                  const isActive = location.pathname === item.path ||
                    (item.path.includes('#') && location.pathname === '/' && location.hash === item.path.split('#')[1]);

                  const handleClick = (e: React.MouseEvent) => {
                    if (item.path.includes('#')) {
                      e.preventDefault();
                      const sectionId = item.path.split('#')[1];
                      const element = document.getElementById(sectionId);
                      if (element) {
                        element.scrollIntoView({ behavior: 'smooth', block: 'start' });
                      }
                    }
                    setIsMobileMenuOpen(false);
                  };

                  return (
                    <Link
                      key={item.path}
                      to={item.path}
                      onClick={handleClick}
                      className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-300 ${
                        isActive
                          ? 'text-cyan-400 bg-cyan-400/10'
                          : 'text-gray-300 hover:text-white hover:bg-gray-700/50'
                      }`}
                    >
                      {item.name}
                    </Link>
                  );
                })}
              </div>
            </motion.div>
          )}
        </div>
      </div>
    </motion.nav>
  );
};

export default Navigation;
